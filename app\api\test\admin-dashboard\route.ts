import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/index';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing admin dashboard data fetch...');
    
    // Test sales analytics
    const salesResult = await db.getSalesAnalytics();
    console.log('Sales result:', salesResult);
    
    // Test low stock products
    const lowStockResult = await db.getLowStockProducts(10);
    console.log('Low stock result:', lowStockResult);
    
    // Test recent orders
    const recentOrdersResult = await db.getOrders({
      page: 1,
      limit: 10,
    });
    console.log('Recent orders result:', recentOrdersResult);

    const dashboardData = {
      sales: salesResult.data || {
        summary: { total_orders: 0, total_revenue: 0, avg_order_value: 0 },
        daily_sales: [],
        top_products: [],
      },
      lowStockProducts: lowStockResult.data || [],
      recentOrders: recentOrdersResult.data || [],
      stats: {
        totalOrders: salesResult.data?.summary?.total_orders || 0,
        totalRevenue: salesResult.data?.summary?.total_revenue || 0,
        avgOrderValue: salesResult.data?.summary?.avg_order_value || 0,
        lowStockCount: lowStockResult.data?.length || 0,
      },
    };

    return NextResponse.json({ 
      success: true,
      data: dashboardData 
    });
  } catch (error) {
    console.error('Test admin dashboard error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch dashboard data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}