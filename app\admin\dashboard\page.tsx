'use client';

import { useEffect, useState } from 'react';
import {
  TrendingUp,
  Users,
  Package,
  ShoppingCart,
  AlertTriangle,
  DollarSign,
  Wifi,
  WifiOff
} from 'lucide-react';
import StatsCard from '@/components/dashboard/StatsCard';
import RecentOrdersTable from '@/components/dashboard/RecentOrdersTable';
import LowStockAlert from '@/components/dashboard/LowStockAlert';
import { useRealtimeDashboard, useAdminOrderNotifications, useLowStockAlerts } from '@/lib/realtime/hooks';

interface DashboardData {
  stats: {
    totalOrders: number;
    totalRevenue: number;
    avgOrderValue: number;
    lowStockCount: number;
  };
  recentOrders: any[];
  lowStockProducts: any[];
  sales: {
    summary: any;
    daily_sales: any[];
    top_products: any[];
  };
}

export default function AdminDashboard() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Realtime hooks
  const realtimeDashboard = useRealtimeDashboard();
  const orderNotifications = useAdminOrderNotifications();
  const lowStockAlerts = useLowStockAlerts();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Refetch data when realtime updates occur
  useEffect(() => {
    if (realtimeDashboard.lastUpdate) {
      fetchDashboardData();
    }
  }, [realtimeDashboard.lastUpdate]);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/admin/dashboard');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }
      const result = await response.json();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
        <p className="text-red-400">Error: {error}</p>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gold mb-2">Admin Dashboard</h1>
          <p className="text-gray-400">Welcome back! Here's what's happening with your store.</p>
        </div>
        
        {/* Realtime Connection Status */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            {realtimeDashboard.isConnected ? (
              <>
                <Wifi className="w-4 h-4 text-green-400" />
                <span className="text-sm text-green-400">Live</span>
              </>
            ) : (
              <>
                <WifiOff className="w-4 h-4 text-red-400" />
                <span className="text-sm text-red-400">Offline</span>
              </>
            )}
          </div>
          
          {/* New Order Notifications */}
          {orderNotifications.notifications.length > 0 && (
            <div className="bg-blue-900/20 border border-blue-500/20 rounded-lg px-3 py-1">
              <span className="text-xs text-blue-400">
                {orderNotifications.notifications.length} new order{orderNotifications.notifications.length > 1 ? 's' : ''}
              </span>
            </div>
          )}
          
          {/* Low Stock Alerts */}
          {lowStockAlerts.alerts.length > 0 && (
            <div className="bg-yellow-900/20 border border-yellow-500/20 rounded-lg px-3 py-1">
              <span className="text-xs text-yellow-400">
                {lowStockAlerts.alerts.length} stock alert{lowStockAlerts.alerts.length > 1 ? 's' : ''}
              </span>
            </div>
          )}
          
          {realtimeDashboard.lastUpdate && (
            <div className="text-xs text-gray-400">
              Updated: {realtimeDashboard.lastUpdate.toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Orders"
          value={data.stats.totalOrders.toString()}
          icon={ShoppingCart}
          trend="+12%"
          trendUp={true}
        />
        <StatsCard
          title="Total Revenue"
          value={`$${data.stats.totalRevenue.toLocaleString()}`}
          icon={DollarSign}
          trend="+8%"
          trendUp={true}
        />
        <StatsCard
          title="Avg Order Value"
          value={`$${data.stats.avgOrderValue.toFixed(2)}`}
          icon={TrendingUp}
          trend="+3%"
          trendUp={true}
        />
        <StatsCard
          title="Low Stock Items"
          value={data.stats.lowStockCount.toString()}
          icon={AlertTriangle}
          trend={data.stats.lowStockCount > 0 ? "Attention needed" : "All good"}
          trendUp={false}
        />
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Orders */}
        <div className="lg:col-span-2">
          <RecentOrdersTable orders={data.recentOrders} />
        </div>

        {/* Low Stock Alert */}
        <div>
          <LowStockAlert products={data.lowStockProducts} />
        </div>
      </div>
    </div>
  );
}